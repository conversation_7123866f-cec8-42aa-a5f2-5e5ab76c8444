import { render } from "@testing-library/react";
import { ThemeScript } from "../components/theme-script";

// Mock para document.cookie
const mockCookie = {
	value: "",
	get cookie() {
		return this.value;
	},
	set cookie(val: string) {
		this.value = val;
	},
};

describe("ThemeScript", () => {
	beforeEach(() => {
		// Mock document.cookie
		Object.defineProperty(document, "cookie", {
			get: () => mockCookie.cookie,
			set: (val: string) => {
				mockCookie.cookie = val;
			},
			configurable: true,
		});

		// Mock window.matchMedia
		Object.defineProperty(window, "matchMedia", {
			writable: true,
			value: (query: string) => ({
				matches: false,
				media: query,
				onchange: null,
				addEventListener: () => {},
				removeEventListener: () => {},
				addListener: () => {},
				removeListener: () => {},
				dispatchEvent: () => false,
			}),
		});

		// Limpar classes do documentElement
		document.documentElement.className = "";
		document.documentElement.removeAttribute("data-theme");
		mockCookie.value = "";
	});

	it("deve renderizar o script sem erros", () => {
		expect(() => render(<ThemeScript />)).not.toThrow();
	});

	it("deve aplicar tema light como padrão quando não há cookie", () => {
		render(<ThemeScript />);

		// Simular execução do script
		const script = document.querySelector("script");
		expect(script).toBeTruthy();

		// O script deve estar presente no DOM
		expect(script?.innerHTML).toContain("getCookie");
		expect(script?.innerHTML).toContain("apply");
		expect(script?.innerHTML).toContain("simp-theme");
	});

	it("deve aplicar tema dark quando cookie contém 'dark'", () => {
		// Configurar cookie com tema dark
		mockCookie.value = 'simp-theme="dark"';

		render(<ThemeScript />);

		const script = document.querySelector("script");
		expect(script).toBeTruthy();
		expect(script?.innerHTML).toContain("dark");
	});

	it("deve conter todas as funções necessárias no script", () => {
		render(<ThemeScript />);

		const script = document.querySelector("script");
		const scriptContent = script?.innerHTML || "";

		// Verificar se contém as funções principais
		expect(scriptContent).toContain("getCookie");
		expect(scriptContent).toContain("isValid");
		expect(scriptContent).toContain("apply");

		// Verificar se contém os temas válidos
		expect(scriptContent).toContain("light");
		expect(scriptContent).toContain("dark");
		expect(scriptContent).toContain("light-green");
		expect(scriptContent).toContain("dark-green");

		// Verificar se contém tratamento de erro
		expect(scriptContent).toContain("try");
		expect(scriptContent).toContain("catch");
	});

	it("deve usar preferência do sistema como fallback", () => {
		// Mock matchMedia para retornar preferência dark
		Object.defineProperty(window, "matchMedia", {
			writable: true,
			value: (query: string) => ({
				matches: query === "(prefers-color-scheme: dark)",
				media: query,
				onchange: null,
				addEventListener: () => {},
				removeEventListener: () => {},
				addListener: () => {},
				removeListener: () => {},
				dispatchEvent: () => false,
			}),
		});

		render(<ThemeScript />);

		const script = document.querySelector("script");
		expect(script?.innerHTML).toContain("prefers-color-scheme: dark");
	});
});
